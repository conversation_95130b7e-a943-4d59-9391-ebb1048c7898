{"Version": 1, "WorkspaceRootPath": "E:\\cursortest\\DarenMIS20250624(1)\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{8AC92D3B-10AD-44F0-9CCC-0443E878AFE7}|DarenApi\\DarenApi.csproj|e:\\cursortest\\darenmis20250624(1)\\darenapi\\onlinestore\\elemeapi.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8AC92D3B-10AD-44F0-9CCC-0443E878AFE7}|DarenApi\\DarenApi.csproj|solutionrelative:darenapi\\onlinestore\\elemeapi.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8AC92D3B-10AD-44F0-9CCC-0443E878AFE7}|DarenApi\\DarenApi.csproj|e:\\cursortest\\darenmis20250624(1)\\darenapi\\onlinestore\\jingdongapi.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8AC92D3B-10AD-44F0-9CCC-0443E878AFE7}|DarenApi\\DarenApi.csproj|solutionrelative:darenapi\\onlinestore\\jingdongapi.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8AC92D3B-10AD-44F0-9CCC-0443E878AFE7}|DarenApi\\DarenApi.csproj|e:\\cursortest\\darenmis20250624(1)\\darenapi\\onlinestore\\pinduoduoapi.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8AC92D3B-10AD-44F0-9CCC-0443E878AFE7}|DarenApi\\DarenApi.csproj|solutionrelative:darenapi\\onlinestore\\pinduoduoapi.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|e:\\cursortest\\darenmis20250624(1)\\darenapp\\dal\\stock\\productstockdal\\drugproductstockdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|solutionrelative:darenapp\\dal\\stock\\productstockdal\\drugproductstockdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|e:\\cursortest\\darenmis20250624(1)\\darenapp\\dal\\stock\\productstockdal\\baseproductstockdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|solutionrelative:darenapp\\dal\\stock\\productstockdal\\baseproductstockdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|e:\\cursortest\\darenmis20250624(1)\\darenapp\\bll\\stock\\productstockbll.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|solutionrelative:darenapp\\bll\\stock\\productstockbll.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|e:\\cursortest\\darenmis20250624(1)\\darenapp\\bll\\bill\\purchasein\\purchaseinbuilder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|solutionrelative:darenapp\\bll\\bill\\purchasein\\purchaseinbuilder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|e:\\cursortest\\darenmis20250624(1)\\darenapp\\doc\\小知识.txt||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|solutionrelative:darenapp\\doc\\小知识.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|e:\\cursortest\\darenmis20250624(1)\\darenapp\\doc\\处理null值.txt||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|solutionrelative:darenapp\\doc\\处理null值.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|e:\\cursortest\\darenmis20250624(1)\\darenapp\\doc\\net core中的options使用.txt||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|solutionrelative:darenapp\\doc\\net core中的options使用.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|e:\\cursortest\\darenmis20250624(1)\\darenapp\\doc\\中间件传值.txt||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|solutionrelative:darenapp\\doc\\中间件传值.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|e:\\cursortest\\darenmis20250624(1)\\darenapp\\doc\\dbcontext 实体更新.txt||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|solutionrelative:darenapp\\doc\\dbcontext 实体更新.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|e:\\cursortest\\darenmis20250624(1)\\darenapp\\doc\\jwt.txt||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|solutionrelative:darenapp\\doc\\jwt.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|e:\\cursortest\\darenmis20250624(1)\\darenapp\\doc\\动态注册服务.txt||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|solutionrelative:darenapp\\doc\\动态注册服务.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|e:\\cursortest\\darenmis20250624(1)\\darenapp\\services\\onlineuserservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|solutionrelative:darenapp\\services\\onlineuserservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|e:\\cursortest\\darenmis20250624(1)\\darenapp\\bll\\sys\\digitsconfigbll.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|solutionrelative:darenapp\\bll\\sys\\digitsconfigbll.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|e:\\cursortest\\darenmis20250624(1)\\darenapp\\dal\\dic\\common\\memberleveldal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|solutionrelative:darenapp\\dal\\dic\\common\\memberleveldal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|e:\\cursortest\\darenmis20250624(1)\\darenapp\\bll\\dic\\memberlevelbll.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|solutionrelative:darenapp\\bll\\dic\\memberlevelbll.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|e:\\cursortest\\darenmis20250624(1)\\darenapp\\bll\\bill\\billbasebuilder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|solutionrelative:darenapp\\bll\\bill\\billbasebuilder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "ElemeApi.cs", "DocumentMoniker": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApi\\OnlineStore\\ElemeApi.cs", "RelativeDocumentMoniker": "DarenApi\\OnlineStore\\ElemeApi.cs", "ToolTip": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApi\\OnlineStore\\ElemeApi.cs", "RelativeToolTip": "DarenApi\\OnlineStore\\ElemeApi.cs", "ViewState": "AgIAAFAAAAAAAAAAAAAUwAsAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T10:37:07.223Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "JingdongApi.cs", "DocumentMoniker": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApi\\OnlineStore\\JingdongApi.cs", "RelativeDocumentMoniker": "DarenApi\\OnlineStore\\JingdongApi.cs", "ToolTip": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApi\\OnlineStore\\JingdongApi.cs", "RelativeToolTip": "DarenApi\\OnlineStore\\JingdongApi.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T10:37:04.344Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "PinduoduoApi.cs", "DocumentMoniker": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApi\\OnlineStore\\PinduoduoApi.cs", "RelativeDocumentMoniker": "DarenApi\\OnlineStore\\PinduoduoApi.cs", "ToolTip": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApi\\OnlineStore\\PinduoduoApi.cs", "RelativeToolTip": "DarenApi\\OnlineStore\\PinduoduoApi.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwgAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T10:36:55.824Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "DrugProductStockDAL.cs", "DocumentMoniker": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\DAL\\Stock\\ProductStockDAL\\DrugProductStockDAL.cs", "RelativeDocumentMoniker": "DarenApp\\DAL\\Stock\\ProductStockDAL\\DrugProductStockDAL.cs", "ToolTip": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\DAL\\Stock\\ProductStockDAL\\DrugProductStockDAL.cs", "RelativeToolTip": "DarenApp\\DAL\\Stock\\ProductStockDAL\\DrugProductStockDAL.cs", "ViewState": "AgIAAF8AAAAAAAAAAAAYwH4AAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T10:30:33.347Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "BaseProductStockDAL.cs", "DocumentMoniker": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\DAL\\Stock\\ProductStockDAL\\BaseProductStockDAL.cs", "RelativeDocumentMoniker": "DarenApp\\DAL\\Stock\\ProductStockDAL\\BaseProductStockDAL.cs", "ToolTip": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\DAL\\Stock\\ProductStockDAL\\BaseProductStockDAL.cs", "RelativeToolTip": "DarenApp\\DAL\\Stock\\ProductStockDAL\\BaseProductStockDAL.cs", "ViewState": "AgIAAAEAAAAAAAAAAAAYwBQAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T10:30:31.351Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "PurchaseInBuilder.cs", "DocumentMoniker": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\BLL\\Bill\\PurchaseIn\\PurchaseInBuilder.cs", "RelativeDocumentMoniker": "DarenApp\\BLL\\Bill\\PurchaseIn\\PurchaseInBuilder.cs", "ToolTip": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\BLL\\Bill\\PurchaseIn\\PurchaseInBuilder.cs", "RelativeToolTip": "DarenApp\\BLL\\Bill\\PurchaseIn\\PurchaseInBuilder.cs", "ViewState": "AgIAAHMBAAAAAAAAAAAswIcBAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T10:30:19.528Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:129:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Document", "DocumentIndex": 7, "Title": "小知识.txt", "DocumentMoniker": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\Doc\\小知识.txt", "RelativeDocumentMoniker": "DarenApp\\Doc\\小知识.txt", "ToolTip": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\Doc\\小知识.txt", "RelativeToolTip": "DarenApp\\Doc\\小知识.txt", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2025-07-31T10:30:02.31Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "处理null值.txt", "DocumentMoniker": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\Doc\\处理null值.txt", "RelativeDocumentMoniker": "DarenApp\\Doc\\处理null值.txt", "ToolTip": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\Doc\\处理null值.txt", "RelativeToolTip": "DarenApp\\Doc\\处理null值.txt", "ViewState": "AgIAAL0AAAAAAAAAAADwv8MAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2025-07-31T07:00:55.561Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "Net Core中的Options使用.txt", "DocumentMoniker": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\Doc\\Net Core中的Options使用.txt", "RelativeDocumentMoniker": "DarenApp\\Doc\\Net Core中的Options使用.txt", "ToolTip": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\Doc\\Net Core中的Options使用.txt", "RelativeToolTip": "DarenApp\\Doc\\Net Core中的Options使用.txt", "ViewState": "AgIAABIAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2025-07-31T07:00:47.114Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "中间件传值.txt", "DocumentMoniker": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\Doc\\中间件传值.txt", "RelativeDocumentMoniker": "DarenApp\\Doc\\中间件传值.txt", "ToolTip": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\Doc\\中间件传值.txt", "RelativeToolTip": "DarenApp\\Doc\\中间件传值.txt", "ViewState": "AgIAADMAAAAAAAAAAADwvw0AAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2025-07-31T07:00:27.528Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "DbContext 实体更新.txt", "DocumentMoniker": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\Doc\\DbContext 实体更新.txt", "RelativeDocumentMoniker": "DarenApp\\Doc\\DbContext 实体更新.txt", "ToolTip": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\Doc\\DbContext 实体更新.txt", "RelativeToolTip": "DarenApp\\Doc\\DbContext 实体更新.txt", "ViewState": "AgIAABIAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2025-07-31T07:00:14.226Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "JWT.txt", "DocumentMoniker": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\Doc\\JWT.txt", "RelativeDocumentMoniker": "DarenApp\\Doc\\JWT.txt", "ToolTip": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\Doc\\JWT.txt", "RelativeToolTip": "DarenApp\\Doc\\JWT.txt", "ViewState": "AgIAADwAAAAAAAAAAADwvw8AAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2025-07-31T07:00:09.01Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "动态注册服务.txt", "DocumentMoniker": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\Doc\\动态注册服务.txt", "RelativeDocumentMoniker": "DarenApp\\Doc\\动态注册服务.txt", "ToolTip": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\Doc\\动态注册服务.txt", "RelativeToolTip": "DarenApp\\Doc\\动态注册服务.txt", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2025-07-31T06:59:56.746Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "OnlineUserService.cs", "DocumentMoniker": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\Services\\OnlineUserService.cs", "RelativeDocumentMoniker": "DarenApp\\Services\\OnlineUserService.cs", "ToolTip": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\Services\\OnlineUserService.cs", "RelativeToolTip": "DarenApp\\Services\\OnlineUserService.cs", "ViewState": "AgIAABwBAAAAAAAAAAAgwCcBAABUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T06:32:30.991Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "ProductStockBLL.cs", "DocumentMoniker": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\BLL\\Stock\\ProductStockBLL.cs", "RelativeDocumentMoniker": "DarenApp\\BLL\\Stock\\ProductStockBLL.cs", "ToolTip": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\BLL\\Stock\\ProductStockBLL.cs", "RelativeToolTip": "DarenApp\\BLL\\Stock\\ProductStockBLL.cs", "ViewState": "AgIAAHkAAAAAAAAAAAAYwJAAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T06:32:03.054Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "DigitsConfigBLL.cs", "DocumentMoniker": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\BLL\\Sys\\DigitsConfigBLL.cs", "RelativeDocumentMoniker": "DarenApp\\BLL\\Sys\\DigitsConfigBLL.cs", "ToolTip": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\BLL\\Sys\\DigitsConfigBLL.cs", "RelativeToolTip": "DarenApp\\BLL\\Sys\\DigitsConfigBLL.cs", "ViewState": "AgIAABYAAAAAAAAAAAAIwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T06:31:41.966Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "MemberLevelDAL.cs", "DocumentMoniker": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\DAL\\Dic\\Common\\MemberLevelDAL.cs", "RelativeDocumentMoniker": "DarenApp\\DAL\\Dic\\Common\\MemberLevelDAL.cs", "ToolTip": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\DAL\\Dic\\Common\\MemberLevelDAL.cs", "RelativeToolTip": "DarenApp\\DAL\\Dic\\Common\\MemberLevelDAL.cs", "ViewState": "AgIAACEAAAAAAAAAAAAawC8AAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T06:31:10.94Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "MemberLevelBLL.cs", "DocumentMoniker": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\BLL\\Dic\\MemberLevelBLL.cs", "RelativeDocumentMoniker": "DarenApp\\BLL\\Dic\\MemberLevelBLL.cs", "ToolTip": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\BLL\\Dic\\MemberLevelBLL.cs", "RelativeToolTip": "DarenApp\\BLL\\Dic\\MemberLevelBLL.cs", "ViewState": "AgIAACgAAAAAAAAAAAAYwDUAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T06:30:52.012Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "BillBaseBuilder.cs", "DocumentMoniker": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\BLL\\Bill\\BillBaseBuilder.cs", "RelativeDocumentMoniker": "DarenApp\\BLL\\Bill\\BillBaseBuilder.cs", "ToolTip": "E:\\cursortest\\DarenMIS20250624(1)\\DarenApp\\BLL\\Bill\\BillBaseBuilder.cs", "RelativeToolTip": "DarenApp\\BLL\\Bill\\BillBaseBuilder.cs", "ViewState": "AgIAAFMAAAAAAAAAAAAcwEYAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T01:49:07.584Z"}]}]}]}