using System;

namespace DarenApi.OnlineStore.Models
{
    public class AccessTokenInfo
    {
        /// <summary>
        /// 应用ID
        /// </summary>
        public string app_id { get; set; }

        /// <summary>
        /// 获取的access token
        /// </summary>
        public string access_token { get; set; }

        /// <summary>
        /// access token有效时间，单位秒
        /// </summary>
        public long at_expires_in { get; set; }

        /// <summary>
        /// access token过期日期
        /// </summary>
        public DateTime? at_expires_date { get; set; }

        /// <summary>
        /// 用于刷新access token的refresh token
        /// </summary>
        public string refresh_token { get; set; }

        /// <summary>
        /// refresh token有效时间，单位秒
        /// </summary>
        public long rt_expires_in { get; set; }

        /// <summary>
        /// refresh token过期日期
        /// </summary>
        public DateTime? rt_expires_date { get; set; }
    }
}
